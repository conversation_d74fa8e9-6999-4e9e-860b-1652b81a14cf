// Database
export { default as prisma } from './database/prisma';

// Types
export * from './types/api-response';

// Utilities
export * from './lib/api-error';
export * from './lib/utils';
export { apiClient } from './lib/api-client';

// Re-export Prisma types
export * from './generated/prisma';

// Re-export generated Prisma types with explicit naming to avoid conflicts
export {
    // Export enums
    $Enums,
    Prisma as GeneratedPrisma,
    PrismaClient as GeneratedPrismaClient,
    ParkingType, ReportType, UserRole,
    // Export model types
    type Event,
    type Floor,
    type Mall,
    type MallHour,
    type Offer,
    type PageView,
    type Report,
    type Shop,
    type ShopHour, type ShopOffer,
    type User,
} from './generated/prisma';
export * from './generated/zod';

