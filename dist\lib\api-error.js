"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.errorHandler = exports.ApiError = void 0;
const server_1 = require("next/server");
class ApiError extends Error {
    constructor(statusCode, message, isOperational = true, stack = '') {
        super(message);
        this.statusCode = statusCode;
        this.isOperational = isOperational;
        if (stack) {
            this.stack = stack;
        }
        else {
            Error.captureStackTrace(this, this.constructor);
        }
    }
}
exports.ApiError = ApiError;
const errorHandler = (err) => {
    if (err instanceof ApiError) {
        return server_1.NextResponse.json({ success: false, error: err.message }, { status: err.statusCode });
    }
    console.error('Unexpected error:', err);
    return server_1.NextResponse.json({ success: false, error: 'Internal Server Error' }, { status: 500 });
};
exports.errorHandler = errorHandler;
