import { ParkingType, UserRole, ReportType } from '../src/generated/prisma';

test('ParkingType enum has correct values', () => {
  expect(ParkingType.free).toBe('free');
  expect(ParkingType.paid).toBe('paid');
  expect(ParkingType.valet).toBe('valet');
});

test('UserRole enum has correct values', () => {
  expect(UserRole.admin).toBe('admin');
  expect(UserRole.mall_manager).toBe('mall_manager');
  expect(UserRole.shop_owner).toBe('shop_owner');
  expect(UserRole.user).toBe('user');
});

test('ReportType enum has correct values', () => {
  expect(ReportType.MALL_ANALYTICS).toBe('MALL_ANALYTICS');
  expect(ReportType.SHOP_ANALYTICS).toBe('SHOP_ANALYTICS');
  expect(ReportType.USER_ANALYTICS).toBe('USER_ANALYTICS');
  expect(ReportType.PAGE_VIEW_ANALYTICS).toBe('PAGE_VIEW_ANALYTICS');
});
