
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for All files</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="prettify.css" />
    <link rel="stylesheet" href="base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1>All files</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">17.86% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>838/4691</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">6.07% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>242/3985</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">7.45% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>103/1382</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">75.91% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>104/137</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file high" data-value="generated/prisma"><a href="generated/prisma/index.html">generated/prisma</a></td>
	<td data-value="92.64" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 92%"></div><div class="cover-empty" style="width: 8%"></div></div>
	</td>
	<td data-value="92.64" class="pct high">92.64%</td>
	<td data-value="68" class="abs high">63/68</td>
	<td data-value="50" class="pct medium">50%</td>
	<td data-value="8" class="abs medium">4/8</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="1" class="abs low">0/1</td>
	<td data-value="92.64" class="pct high">92.64%</td>
	<td data-value="68" class="abs high">63/68</td>
	</tr>

<tr>
	<td class="file low" data-value="generated/prisma/runtime"><a href="generated/prisma/runtime/index.html">generated/prisma/runtime</a></td>
	<td data-value="16.48" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 16%"></div><div class="cover-empty" style="width: 84%"></div></div>
	</td>
	<td data-value="16.48" class="pct low">16.48%</td>
	<td data-value="4603" class="abs low">759/4603</td>
	<td data-value="5.89" class="pct low">5.89%</td>
	<td data-value="3971" class="abs low">234/3971</td>
	<td data-value="7.32" class="pct low">7.32%</td>
	<td data-value="1378" class="abs low">101/1378</td>
	<td data-value="52" class="pct medium">52%</td>
	<td data-value="50" class="abs medium">26/50</td>
	</tr>

<tr>
	<td class="file high" data-value="lib"><a href="lib/index.html">lib</a></td>
	<td data-value="80" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 80%"></div><div class="cover-empty" style="width: 20%"></div></div>
	</td>
	<td data-value="80" class="pct high">80%</td>
	<td data-value="20" class="abs high">16/20</td>
	<td data-value="66.66" class="pct medium">66.66%</td>
	<td data-value="6" class="abs medium">4/6</td>
	<td data-value="66.66" class="pct medium">66.66%</td>
	<td data-value="3" class="abs medium">2/3</td>
	<td data-value="78.94" class="pct medium">78.94%</td>
	<td data-value="19" class="abs medium">15/19</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-06-24T15:16:09.370Z
            </div>
        <script src="prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="sorter.js"></script>
        <script src="block-navigation.js"></script>
    </body>
</html>
    