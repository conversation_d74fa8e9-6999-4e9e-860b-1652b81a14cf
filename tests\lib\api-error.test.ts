import { ApiError } from '../../src/lib/api-error';

test('ApiError creates error with correct properties', () => {
  const error = new ApiError(400, 'Bad Request');

  expect(error).toBeInstanceOf(Error);
  expect(error.statusCode).toBe(400);
  expect(error.message).toBe('Bad Request');
  expect(error.isOperational).toBe(true);
});

test('ApiError accepts custom operational flag', () => {
  const error = new ApiError(500, 'Internal Error', false);

  expect(error.statusCode).toBe(500);
  expect(error.isOperational).toBe(false);
});

test('ApiError accepts custom stack trace', () => {
  const customStack = 'Custom stack trace';
  const error = new ApiError(404, 'Not Found', true, customStack);

  expect(error.stack).toBe(customStack);
});
