{"name": "@mallsurf-packages/core", "version": "1.0.1", "main": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"require": "./dist/index.js", "import": "./dist/index.js", "types": "./dist/index.d.ts"}}, "publishConfig": {"registry": "https://npm.pkg.github.com", "@mallsurf-packages:registry": "https://npm.pkg.github.com"}, "repository": {"type": "git", "url": "git+https://github.com/mallsurf-packages/mallsurf-core.git"}, "scripts": {"build": "npm run db:generate && tsc --outDir dist && npm run copy-prisma", "copy-prisma": "node scripts/copy-prisma.js", "dev": "tsc --watch", "clean": "<PERSON><PERSON><PERSON> dist", "db:generate": "prisma generate --schema=src/database/schema.prisma", "prepublishOnly": "npm run clean && npm run build", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false"}, "dependencies": {"@prisma/client": "^6.10.1", "add": "^2.0.6", "axios": "^1.10.0", "clsx": "^2.1.1", "next": "15.3.4", "prisma": "^6.10.1", "tailwind-merge": "^3.3.1", "zod": "^3.25.67", "zod-prisma-types": "^3.2.4"}, "devDependencies": {"@jest/globals": "^30.0.2", "@types/jest": "^30.0.0", "@types/node": "^20", "jest": "^30.0.2", "rimraf": "^5.0.0", "ts-jest": "^29.4.0", "typescript": "^5"}, "files": ["dist/**/*", "README.md"], "keywords": ["mallsurf", "core", "database", "utilities"]}