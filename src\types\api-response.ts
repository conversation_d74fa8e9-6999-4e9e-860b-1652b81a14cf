export interface ApiResponse<T> {
    success: boolean;
    message?: string;
    data?: T;
    error?: string;
    errors?: string[];
    status?: number;
    code?: string;
    stack?: string;
    meta?: {
        page?: number;
        limit?: number;
        total?: number;
        hasNext?: boolean;
    };
}

export type ApiHandler<T> = (
    req: Request,
    res: Response
) => Promise<Response | ApiResponse<T>>;

// Helper function to create a successful API response
export const createSuccessResponse = <T>(data: T, message?: string): ApiResponse<T> => ({
    success: true,
    data,
    message
});

// Helper function to create an error API response
export const createErrorResponse = (error: string, status?: number, code?: string): ApiResponse<never> => ({
    success: false,
    error,
    status,
    code
});
