"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserRole = exports.ReportType = exports.ParkingType = exports.Prisma = exports.$Enums = exports.PrismaClient = exports.apiClient = exports.prisma = void 0;
// Database
var prisma_1 = require("./database/prisma");
Object.defineProperty(exports, "prisma", { enumerable: true, get: function () { return __importDefault(prisma_1).default; } });
// Utilities
__exportStar(require("./lib/api-error"), exports);
__exportStar(require("./lib/utils"), exports);
var api_client_1 = require("./lib/api-client");
Object.defineProperty(exports, "apiClient", { enumerable: true, get: function () { return api_client_1.apiClient; } });
// Re-export Prisma types
var prisma_2 = require("./generated/prisma");
// Export the PrismaClient class
Object.defineProperty(exports, "PrismaClient", { enumerable: true, get: function () { return prisma_2.PrismaClient; } });
// Export enums
Object.defineProperty(exports, "$Enums", { enumerable: true, get: function () { return prisma_2.$Enums; } });
Object.defineProperty(exports, "Prisma", { enumerable: true, get: function () { return prisma_2.Prisma; } });
Object.defineProperty(exports, "ParkingType", { enumerable: true, get: function () { return prisma_2.ParkingType; } });
Object.defineProperty(exports, "ReportType", { enumerable: true, get: function () { return prisma_2.ReportType; } });
Object.defineProperty(exports, "UserRole", { enumerable: true, get: function () { return prisma_2.UserRole; } });
__exportStar(require("./generated/zod"), exports);
