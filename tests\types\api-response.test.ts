import type { ApiResponse } from '../../src/types/api-response';

test('ApiResponse type works with success response', () => {
  const response: ApiResponse<{ id: number }> = {
    success: true,
    data: { id: 1 }
  };

  expect(response.success).toBe(true);
  expect(response.data?.id).toBe(1);
});

test('ApiResponse type works with error response', () => {
  const response: ApiResponse<null> = {
    success: false,
    error: 'Something went wrong'
  };

  expect(response.success).toBe(false);
  expect(response.error).toBe('Something went wrong');
});

test('ApiResponse type works with pagination', () => {
  const response: ApiResponse<number[]> = {
    success: true,
    data: [1, 2, 3],
    meta: { page: 1, total: 10 }
  };

  expect(response.data).toEqual([1, 2, 3]);
  expect(response.meta?.page).toBe(1);
});
